/**
 * CliniCore Patient Synchronization
 *
 * Provides patient synchronization operations for CliniCore webhook events.
 * Handles database lookups, sync buffer checks, AutoPatient API integration,
 * and database updates with comprehensive error handling and logging.
 *
 * @fileoverview Patient synchronization utilities for CC webhook processing
 * @version 1.0.0
 * @since 2024-07-27
 */

import { contactReq } from "@apiClient";
import { dbSchema, getDb } from "@database";
import type {
	GetAPContactType,
	GetCCPatientType,
	PostAPContactType,
} from "@type";
import { eq } from "drizzle-orm";
import { logApiError, logDatabaseError } from "@/utils/errorLogger";
import { logDebug, logError, logInfo } from "@/utils/logger";

import type {
	APContactUpsertResponse,
	PatientInsert,
	PatientLookupResult,
	PatientSelect,
	SyncBufferCheckResult,
} from "./types";

/**
 * Look up existing patient record in the database
 *
 * Searches for an existing patient record using multiple strategies:
 * 1. Primary lookup by CliniCore patient ID (ccId)
 * 2. Secondary lookup by email and phone for potential matches
 *
 * @param ccPatientId - CliniCore patient ID
 * @param email - Patient email address
 * @param phone - Patient phone number
 * @param requestId - Request ID for logging and tracing
 * @returns Patient lookup result with found record and lookup method
 *
 * @example
 * ```typescript
 * const lookupResult = await lookupExistingPatient(1766, "<EMAIL>", "+**********", "req-123");
 * if (lookupResult.found) {
 *   console.log(`Found patient via ${lookupResult.lookupMethod}`);
 * }
 * ```
 */
export async function lookupExistingPatient(
	ccPatientId: number,
	email?: string,
	phone?: string,
	requestId?: string,
): Promise<PatientLookupResult> {
	const db = getDb();

	try {
		logDebug(`[${requestId}] Looking up patient with CC ID: ${ccPatientId}`);

		// Primary lookup by ccId
		const patientByCcId = await db
			.select()
			.from(dbSchema.patient)
			.where(eq(dbSchema.patient.ccId, ccPatientId))
			.limit(1);

		if (patientByCcId.length > 0) {
			logDebug(`[${requestId}] Found patient by CC ID: ${ccPatientId}`);
			return {
				found: true,
				patient: patientByCcId[0],
				lookupMethod: "ccId",
			};
		}

		// Secondary lookup by email and phone
		if (email || phone) {
			logDebug(`[${requestId}] CC ID not found, trying email/phone lookup`);

			// For JSONB field queries, we need to use a different approach
			// Since direct JSONB field access in where clauses is complex,
			// we'll fetch all patients and filter in memory for now
			// TODO: Optimize with proper JSONB queries if needed
			const allPatients = await db.select().from(dbSchema.patient).limit(1000); // Reasonable limit for filtering

			const matchingPatients = allPatients.filter((patient) => {
				// Check email matches
				if (email) {
					const apEmail = patient.apData?.email;
					const ccEmail = patient.ccData?.email;
					if (apEmail === email || ccEmail === email) {
						return true;
					}
				}

				// Check phone matches
				if (phone) {
					const apPhone = patient.apData?.phone;
					const ccPhoneMobile = patient.ccData?.phoneMobile;
					const ccPhonePersonal = patient.ccData?.phonePersonal;
					const ccPhoneBusiness = patient.ccData?.phoneBusiness;

					if (
						apPhone === phone ||
						ccPhoneMobile === phone ||
						ccPhonePersonal === phone ||
						ccPhoneBusiness === phone
					) {
						return true;
					}
				}

				return false;
			});

			if (matchingPatients.length > 0) {
				const lookupMethod =
					email && phone ? "email_and_phone" : email ? "email" : "phone";
				logInfo(
					`[${requestId}] Found potential patient match by ${lookupMethod} for CC ID: ${ccPatientId}`,
				);
				return {
					found: true,
					patient: matchingPatients[0],
					lookupMethod,
				};
			}
		}

		logDebug(
			`[${requestId}] No existing patient found for CC ID: ${ccPatientId}`,
		);
		return { found: false };
	} catch (error) {
		await logDatabaseError(
			error instanceof Error ? error : new Error(String(error)),
			"patient_lookup",
		);
		logError(`[${requestId}] Database error during patient lookup: ${error}`);
		return { found: false };
	}
}

/**
 * Check if event should be processed based on sync buffer timing
 *
 * Compares the webhook updatedAt timestamp with the database ccUpdatedAt
 * timestamp to determine if the event is within the sync buffer window.
 * This prevents sync loops and unnecessary processing.
 *
 * @param webhookUpdatedAt - Updated timestamp from webhook payload
 * @param dbCcUpdatedAt - Last CC updated timestamp from database
 * @param bufferTimeSec - Sync buffer time in seconds
 * @param requestId - Request ID for logging
 * @returns Sync buffer check result with processing decision
 *
 * @example
 * ```typescript
 * const bufferCheck = checkSyncBuffer(
 *   "2024-07-27T10:30:00.000Z",
 *   new Date("2024-07-27T10:29:30.000Z"),
 *   60,
 *   "req-123"
 * );
 * if (!bufferCheck.shouldProcess) {
 *   console.log(`Skipping: ${bufferCheck.reason}`);
 * }
 * ```
 */
export function checkSyncBuffer(
	webhookUpdatedAt: string,
	dbCcUpdatedAt: Date | null,
	bufferTimeSec: number,
	requestId?: string,
): SyncBufferCheckResult {
	const webhookTime = new Date(webhookUpdatedAt);

	if (!dbCcUpdatedAt) {
		logDebug(`[${requestId}] No previous CC timestamp, processing event`);
		return {
			shouldProcess: true,
			reason: "No previous CC timestamp found",
			bufferThreshold: bufferTimeSec,
		};
	}

	const timeDifference =
		Math.abs(webhookTime.getTime() - dbCcUpdatedAt.getTime()) / 1000;
	const bufferThreshold = bufferTimeSec;

	if (timeDifference <= bufferThreshold) {
		logInfo(
			`[${requestId}] Event within sync buffer (${timeDifference.toFixed(1)}s <= ${bufferThreshold}s), skipping`,
		);
		return {
			shouldProcess: false,
			reason: `Event within sync buffer window (${timeDifference.toFixed(1)}s)`,
			timeDifference,
			bufferThreshold,
		};
	}

	logDebug(
		`[${requestId}] Event outside sync buffer (${timeDifference.toFixed(1)}s > ${bufferThreshold}s), processing`,
	);
	return {
		shouldProcess: true,
		reason: `Event outside sync buffer window (${timeDifference.toFixed(1)}s)`,
		timeDifference,
		bufferThreshold,
	};
}

/**
 * Upsert contact in AutoPatient
 *
 * Creates or updates a contact in AutoPatient using the mapped field data.
 * Uses AutoPatient's native upsert functionality to handle duplicate contacts gracefully.
 * Falls back to update if upsert fails and an existing AP ID is available.
 *
 * @param apContactData - Mapped AutoPatient contact data
 * @param existingApId - Existing AP contact ID (for fallback updates)
 * @param requestId - Request ID for logging and error tracking
 * @returns AutoPatient API response with contact data or error details
 */
export async function upsertApContact(
	apContactData: Partial<PostAPContactType>,
	existingApId?: string,
	requestId?: string,
): Promise<APContactUpsertResponse> {
	try {
		logDebug(`[${requestId}] Upserting AP contact`);

		let response: GetAPContactType;

		try {
			// Primary approach: Use AutoPatient's native upsert functionality
			// This handles duplicate contacts gracefully by updating existing ones
			response = await contactReq.upsert(apContactData as PostAPContactType);
			logInfo(`[${requestId}] Upserted AP contact: ${response.id}`);
		} catch (upsertError) {
			const upsertErrorMessage = upsertError instanceof Error ? upsertError.message : String(upsertError);

			// Check if this is a duplicate contact error and we have an existing AP ID to fall back to
			if (existingApId && (
				upsertErrorMessage.toLowerCase().includes("duplicated contacts") ||
				upsertErrorMessage.toLowerCase().includes("duplicate") ||
				upsertErrorMessage.toLowerCase().includes("already exists")
			)) {
				logInfo(`[${requestId}] Upsert failed with duplicate error, falling back to update with existing AP ID: ${existingApId}`);

				// Fallback: Update the existing contact directly
				response = await contactReq.update(
					existingApId,
					apContactData as PostAPContactType,
				);
				logInfo(`[${requestId}] Updated AP contact via fallback: ${existingApId}`);
			} else {
				// Re-throw the error if it's not a duplicate contact issue or we don't have a fallback ID
				throw upsertError;
			}
		}

		return {
			success: true,
			contact: response,
			statusCode: 200,
		};
	} catch (error) {
		const errorMessage = error instanceof Error ? error.message : String(error);

		await logApiError(
			error instanceof Error ? error : new Error(errorMessage),
			"autopatient",
			"POST /contacts/upsert/",
			apContactData,
		);

		logError(`[${requestId}] AP API error: ${errorMessage}`);

		return {
			success: false,
			error: errorMessage,
			statusCode: 500,
		};
	}
}

/**
 * Update patient record in database
 *
 * Updates the patient record with new data from both CliniCore and AutoPatient,
 * including sync timestamps and complete data preservation.
 *
 * @param patientId - Database patient ID
 * @param ccPatient - CliniCore patient data
 * @param apContact - AutoPatient contact data
 * @param requestId - Request ID for logging
 * @returns Updated patient record or null if failed
 */
export async function updatePatientRecord(
	patientId: string,
	ccPatient: GetCCPatientType,
	apContact?: GetAPContactType,
	requestId?: string,
): Promise<PatientSelect | null> {
	const db = getDb();

	try {
		const updateData: Partial<PatientInsert> = {
			ccData: ccPatient,
			ccUpdatedAt: new Date(ccPatient.updatedAt),
		};

		if (apContact) {
			updateData.apData = apContact;
			updateData.apId = apContact.id;
			updateData.apUpdatedAt = new Date();
		}

		const updatedPatients = await db
			.update(dbSchema.patient)
			.set(updateData)
			.where(eq(dbSchema.patient.id, patientId))
			.returning();

		if (updatedPatients.length > 0) {
			logInfo(`[${requestId}] Updated patient record: ${patientId}`);
			return updatedPatients[0];
		}

		logError(`[${requestId}] Failed to update patient record: ${patientId}`);
		return null;
	} catch (error) {
		await logDatabaseError(
			error instanceof Error ? error : new Error(String(error)),
			"patient_update",
		);
		logError(`[${requestId}] Database error updating patient: ${error}`);
		return null;
	}
}

/**
 * Create new patient record in database
 *
 * Creates a new patient record with data from both CliniCore and AutoPatient.
 *
 * @param ccPatient - CliniCore patient data
 * @param apContact - AutoPatient contact data
 * @param requestId - Request ID for logging
 * @returns Created patient record or null if failed
 */
export async function createPatientRecord(
	ccPatient: GetCCPatientType,
	apContact: GetAPContactType,
	requestId?: string,
): Promise<PatientSelect | null> {
	const db = getDb();

	try {
		const patientData: PatientInsert = {
			ccId: ccPatient.id,
			apId: apContact.id,
			ccData: ccPatient,
			apData: apContact,
			ccUpdatedAt: new Date(ccPatient.updatedAt),
			apUpdatedAt: new Date(),
		};

		const createdPatients = await db
			.insert(dbSchema.patient)
			.values(patientData)
			.returning();

		if (createdPatients.length > 0) {
			logInfo(
				`[${requestId}] Created new patient record: ${createdPatients[0].id}`,
			);
			return createdPatients[0];
		}

		logError(
			`[${requestId}] Failed to create patient record for CC ID: ${ccPatient.id}`,
		);
		return null;
	} catch (error) {
		await logDatabaseError(
			error instanceof Error ? error : new Error(String(error)),
			"patient_create",
		);
		logError(`[${requestId}] Database error creating patient: ${error}`);
		return null;
	}
}
