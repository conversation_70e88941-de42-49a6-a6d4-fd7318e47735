/**
 * Custom Fields Synchronization Handler
 *
 * HTTP handler for custom field synchronization between AutoPatient (AP)
 * and CliniCore (CC) platforms. This module contains only the HTTP request
 * handler function, with all business logic extracted to dedicated processor
 * modules for better maintainability and testability.
 *
 * Features:
 * - Clean HTTP request/response handling
 * - Request ID extraction and correlation
 * - Error handling and response formatting
 * - Integration with custom fields processor modules
 *
 * @fileoverview HTTP handler for custom field synchronization
 * @version 2.0.0
 * @since 2024-07-27
 */

import type { Context } from "hono";
import { synchronizeCustomFields } from "@/processors/customFields";
import { logError } from "@/utils/logger";

/**
 * Handle Custom Fields webhook events
 *
 * HTTP handler function that processes custom field synchronization requests.
 * Extracts the request ID from the Hono context, delegates the synchronization
 * logic to the processor module, and returns a properly formatted JSON response.
 *
 * This handler is designed to be:
 * - **Lightweight**: Contains only HTTP-specific logic
 * - **Focused**: Single responsibility for request/response handling
 * - **Traceable**: Includes request ID correlation for debugging
 * - **Resilient**: Comprehensive error handling with proper HTTP status codes
 * - **Secure**: Sanitized responses that don't expose internal field data or configurations
 *
 * @param c - Hono context object containing request data and utilities
 * @returns Promise resolving to HTTP Response with synchronization results
 *
 * @example
 * ```typescript
 * // Used in Hono route definition
 * app.get('/cf', cfHandler);
 *
 * // Response format on success (200):
 * {
 *   "requestId": "req-123",
 *   "status": "success",
 *   "message": "Custom field synchronization completed",
 *   "metadata": {
 *     "timestamp": "2024-07-27T10:30:00.000Z",
 *     "statistics": {
 *       "totalProcessed": 45,
 *       "totalMatched": 30,
 *       "totalUnmatched": 15,
 *       "totalStandardMappings": 5
 *     },
 *     "creationStatistics": {
 *       "totalCreated": 8,
 *       "creationErrors": 1,
 *       "creationSkippedDueToStandardFields": 2,
 *       "creationBlockedCount": 3
 *     },
 *     "summary": {
 *       "matchedCount": 30,
 *       "upsertedCount": 30,
 *       "unmatchedFieldsCount": 15,
 *       "createdFieldsCount": 8,
 *       "blockedFieldsCount": 3,
 *       "errorsCount": 1
 *     }
 *   }
 * }
 *
 * // Response format on error (500):
 * {
 *   "requestId": "req-123",
 *   "status": "error",
 *   "message": "Custom field synchronization failed",
 *   "metadata": {
 *     "timestamp": "2024-07-27T10:30:00.000Z"
 *   }
 * }
 * ```
 *
 * @since 1.0.0
 */
export async function cfHandler(c: Context): Promise<Response> {
	const requestId = c.get("requestId");
	const timestamp = new Date().toISOString();

	try {
		const syncResult = await synchronizeCustomFields(requestId);

		// Create sanitized response with only public statistics
		return c.json(
			{
				requestId,
				status: "success",
				message: "Custom field synchronization completed",
				metadata: {
					timestamp,
					statistics: {
						totalProcessed: syncResult.statistics.totalProcessed,
						totalMatched: syncResult.statistics.totalMatched,
						totalUnmatched: syncResult.statistics.totalUnmatched,
						totalStandardMappings: syncResult.statistics.totalStandardMappings,
					},
					creationStatistics: {
						totalCreated: syncResult.creationStatistics.totalCreated,
						creationErrors: syncResult.creationStatistics.creationErrors,
						creationSkippedDueToStandardFields: syncResult.creationStatistics.creationSkippedDueToStandardFields,
						creationBlockedCount: syncResult.creationStatistics.creationBlockedCount,
					},
					summary: {
						matchedCount: syncResult.matchedCount,
						upsertedCount: syncResult.upsertedCount,
						unmatchedFieldsCount: syncResult.unmatchedApFields.length + syncResult.unmatchedCcFields.length,
						createdFieldsCount: syncResult.createdCcFields.length + syncResult.createdApFields.length,
						blockedFieldsCount: syncResult.blockedApFields.length,
						errorsCount: syncResult.errors.length + syncResult.creationErrors.length,
					},
				},
			},
			200,
		);
	} catch (error) {
		logError("Custom field handler failed", error);
		return c.json(
			{
				requestId,
				status: "error",
				message: "Custom field synchronization failed",
				metadata: {
					timestamp,
				},
			},
			500,
		);
	}
}
